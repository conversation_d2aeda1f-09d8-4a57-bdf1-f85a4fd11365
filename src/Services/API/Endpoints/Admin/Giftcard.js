import api from '../../api_congig';



// get the giftproduct

export const get_gift_product_name = () => {
    return api.get('/giftproduct/');
};

//put update the gift card
export const update_gift_product_name = (id, payload) => {
  return api.put(`/giftproduct/${id}/`, payload);
};

// Add Gift Product name
export const add_gift_product_name = (payload) => {
    return api.post('/giftproduct/', payload);
};

export const delete_giftproduct_name = (id) => {
    return api.delete(`/giftproduct/${id}/`);
  };



// Add the Gift Type Digital voucher
export const add_gift_type = (payload) => {
    return api.post('/giftproduct_type/', payload);
};


// update the gift Type Digital Voucher
export const update_gift_type = (id,payload) => {
  return api.put(`/giftproduct_type/${id}/`, payload);
};



// Get all get_add_giftproduct_type
export const get_add_giftproduct_type = () => {
  return api.get('/giftproduct_type/');
};


export const delete_giftproduct_type = (id) => {
    return api.delete(`/giftproduct_type/${id}/`);
  };

// Add the Gift card 
export const add_giftcard_Form = (payload) => {
    return api.post('/giftproduct_options/', payload);
};

// Update the Gift card
export const update_giftcard_Form = (id, payload) => {
    return api.put(`/giftproduct_options/${id}/`, payload);
};

// Get all get_all_giftproduct_name
export const get_all_giftcard = () => {
  return api.get('/giftproduct_options/');
};

export const delete_giftcard_Form = (id) => {
    return api.delete(`/giftproduct_options/${id}/`);
};




