import api from '../../api_congig';


// ---------------  PRODUCTS CATEGORY
// List the product

export const list_product_category = () => {
    return api.get('/ecommerce/product_category/');
};


// Add  Product category
export const add_product_category = (payload) => {
    return api.post('/ecommerce/product_category/', payload);
};



// ---------------  PRODUCTS 

// Update the Gift card
export const list_products_by_category = (id) => {
    return api.get(`/ecommerce/product/?category=${id}`);
};

// Add the product 
export const add_product = (payload) => {
  return api.post('/ecommerce/product/', payload);
};


// Update the Gift card
export const update_product = (id, payload) => {
  return api.put(`/ecommerce/product/${id}/`, payload);
};

// List the Individual Product

export const list_product = (id) => {
  return api.get(`/ecommerce/product/${id}/`);
};

// delete the products varaint image
export const delete_product_variant_image = (id) => {
  return api.delete(`/ecommerce/product_variant_image/${id}/`);
};


// Not worked from backend Delete specific all variant based on id
export const delete_productVariant = (id) => {
  return api.delete(`/ecommerce/product_variant/${id}/`);
};


// delete the product images 
export const delete_product_image = (id) => {
  return api.delete(`/ecommerce/product_image/${id}/`);
};


// delete the product images 
export const delete_product= (id) => {
  return api.delete(`/ecommerce/product/${id}/`);
};








export const delete_giftproduct_name = (id) => {
    return api.delete(`/giftproduct/${id}/`);
  };





// Get all get_add_giftproduct_type
export const get_add_giftproduct_type = () => {
  return api.get('/giftproduct_type/');
};


export const delete_giftproduct_type = (id) => {
    return api.delete(`/giftproduct_type/${id}/`);
  };

// Add the Gift card 
export const add_giftcard_Form = (payload) => {
    return api.post('/giftproduct_options/', payload);
};

// Update the Gift card
export const update_giftcard_Form = (id, payload) => {
    return api.put(`/giftproduct_options/${id}/`, payload);
};

// Get all get_all_giftproduct_name
export const get_all_giftcard = () => {
  return api.get('/giftproduct_options/');
};








