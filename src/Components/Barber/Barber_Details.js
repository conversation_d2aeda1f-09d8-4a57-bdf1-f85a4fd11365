import {
    KeyboardAvoidingView,
    Platform,
    SafeAreaView,
    StyleSheet,
    View,
    TouchableOpacity,
    Alert,
    Image
  } from 'react-native';
  import React, {useState, useEffect} from 'react';
  import {ScrollView, TextInput} from 'react-native-gesture-handler';
  import CustomHeader from '../CustomHeader';
  import useAppText from '../../Custom/AppText';
  import CustomTextInput from '../CustomTextInput';
  import {hp, wp} from '../../Custom/Responsiveness';
  import useTheme from '../../Redux/useTheme';
  import ResponsiveText from '../../Custom/RnText';
  import ImageUploadcard from './ImageUploadcard';
  import { colors } from '../../Custom/Colors';
  import Loader from '../../Custom/loader';
  import {globalpath} from '../../Custom/globalpath';
import { delete_barber, update_barber, update_barber_working_hours } from '../../Services/API/Endpoints/Admin/Barber';
import Add_Barber_Week_Card from './Add_Barber_Week_Card';
import Add_Barber_Time_Picker_Modal from './Add_Barber_Time_Picker_Modal';
import Icon from '../../Custom/Icon';

const BarberDetails = ({navigation, route}) => {
    const {getTextColor, getDark_Theme, backgroundColor , getDarK_mode_LightGrayBackground} = useTheme();
    const {barber} = route.params;
    const AppText = useAppText();
    const [loading, setLoading] = useState(false);
    console.log("the barber details are in params is ----- --------",barber)
    console.log("working hours inside the edit screen",barber.working_hours)

    const barberID=barber?.id
    console.log("the barber id is --------",barberID)

    const [formData, setFormData] = useState({
      full_name: '',
      email: '',
      phone_number: '',
      description: '',
      profile_image: null
    });

    const [selectedImage, setSelectedImage] = useState(null);

    useEffect(() => {
      if (barber) {
        setFormData({
          full_name: barber.full_name || '',
          email: barber.email || '',
          phone_number: barber.phone_number || '',
          description: barber.description || '',
          profile_image: barber.profile_image || null
        });
      }
    }, [barber]);

    const handleImageSelect = (imageDetails) => {
      setSelectedImage(imageDetails);
      if (imageDetails) {
        console.log('Selected Image Details:', {
          name: imageDetails.fileName,
          size: imageDetails.size + ' MB',
          dimensions: imageDetails.dimensions,
          uri: imageDetails.uri
        });
      } else {
        console.log('Image removed');
      }
    };

    const handleChange = (field, value) => {
      setFormData(prevState => ({
        ...prevState,
        [field]: value
      }));
    };

    const handleSubmit = async () => {
      if (!formData.full_name || !formData.email || !formData.phone_number) {
        Alert.alert('Validation Error', 'Please fill in all required fields.');
        return;
      }

      try {
        setLoading(true);

        // Build payload
        const updatedData = {
          full_name: formData.full_name,
          email: formData.email,
          phone_number: formData.phone_number,
          description: formData.description,
        };

        // If new image selected, append it as FormData (assuming backend expects multipart)
        let payload;

        if (selectedImage) {
          const form = new FormData();
          form.append('full_name', updatedData.full_name);
          form.append('email', updatedData.email);
          form.append('phone_number', updatedData.phone_number);
          form.append('description', updatedData.description);
          form.append('profile_image', {
            uri: selectedImage.uri,
            type: selectedImage.type || 'image/jpeg',
            name: selectedImage.fileName || 'profile.jpg',
          });
          payload = form;
        } else {
          payload = updatedData;
        }

        // Make API call
        await update_barber(barberID, payload);

        Alert.alert('Success', 'Barber details updated successfully!');
        navigation.pop(2);

        // navigation.dispatch(
        //   CommonActions.reset({
        //     index: 0,
        //     routes: [{ name: 'Barber' }],
        //   })
        // );      } catch (error) {
        console.error('Error updating barber:', error.response || error.message);
        Alert.alert('Error', 'Failed to update barber details');
      } finally {
        setLoading(false);
      }
    };



    const [weekDays, setWeekDays] = useState([]);
    const [selectedDay, setSelectedDay] = useState(null);
    const [showTimeModal, setShowTimeModal] = useState(false);
    const [workingHours, setWorkingHours] = useState([
      { day_of_week: 0, open_time: '00:00:00', close_time: '00:00:00' }, // Mon
      { day_of_week: 1, open_time: '00:00:00', close_time: '00:00:00' },
      { day_of_week: 2, open_time: '00:00:00', close_time: '00:00:00' },
      { day_of_week: 3, open_time: '00:00:00', close_time: '00:00:00' },
      { day_of_week: 4, open_time: '00:00:00', close_time: '00:00:00' },
      { day_of_week: 5, open_time: '00:00:00', close_time: '00:00:00' },
      { day_of_week: 6, open_time: '00:00:00', close_time: '00:00:00' },
    ]);

    const dayMap = {
      [AppText.MON]: 0,
      [AppText.TUE]: 1,
      [AppText.WED]: 2,
      [AppText.THU]: 3,
      [AppText.FRI]: 4,
      [AppText.SAT]: 5,
      [AppText.SUN]: 6,
    };

    const formatTimeToAMPM = (timeString) => {
      if (!timeString) return '';
      const [hours, minutes] = timeString.split(':');
      const date = new Date();
      date.setHours(parseInt(hours));
      date.setMinutes(parseInt(minutes));
      return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });
    };

    useEffect(() => {
      generateWeekDays();
    }, [barber.working_hours]);

    const generateWeekDays = () => {
      const days = [
        AppText.MON,
        AppText.TUE,
        AppText.WED,
        AppText.THU,
        AppText.FRI,
        AppText.SAT,
        AppText.SUN
      ];

      const weekDaysData = days.map((day, index) => {
        const workingHour = barber.working_hours?.find(wh => wh.day_of_week === index);
        const hasWorkingHours = workingHour && workingHour.start_time && workingHour.end_time;

        return {
          day,
          isSelected: hasWorkingHours,
          selectedTime: hasWorkingHours
            ? `${formatTimeToAMPM(workingHour.start_time)} - ${formatTimeToAMPM(workingHour.end_time)}`
            : null,
        };
      });

      setWeekDays(weekDaysData);

      if (barber.working_hours) {
        setWorkingHours(barber.working_hours.map(wh => ({
          day_of_week: wh.day_of_week,
          open_time: wh.start_time || '00:00:00',
          close_time: wh.end_time || '00:00:00'
        })));
      }
    };

    const handleDayPress = (index) => {
      setSelectedDay(index);
      setShowTimeModal(true);
    };

    const formatTo24Hour = (date) => {
      if (!(date instanceof Date)) return '00:00:00';
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${hours}:${minutes}:00`;
    };

    const handleTimeSelect = async (startTime, endTime) => {
      try {
        const displayStart = startTime.toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true,
        });

        const displayEnd = endTime.toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true,
        });

        const displayString = `${displayStart} - ${displayEnd}`;

        setWeekDays(prev => {
          const updated = [...prev];
          updated[selectedDay] = {
            ...updated[selectedDay],
            isSelected: true,
            selectedTime: displayString,
          };
          return updated;
        });

        const dayName = weekDays[selectedDay].day;
        const dayOfWeek = dayMap[dayName];

        // Find the working hour object for the selected day
        const workingHourToUpdate = barber.working_hours.find(wh => wh.day_of_week === dayOfWeek);

        if (workingHourToUpdate) {
          // Create updated working hour object
          const updatedHour = {
            ...workingHourToUpdate,
            start_time: formatTo24Hour(startTime),
            end_time: formatTo24Hour(endTime)
          };

          console.log("🕒 Updating working hour:", updatedHour);

          // Make API call to update working hours
          const response = await update_barber_working_hours(updatedHour.id, updatedHour);
          console.log('✅ Working hours updated successfully:', response);

          // Update local state
          setWorkingHours(prev => {
            const updatedHours = prev.map(hour =>
              hour.day_of_week === dayOfWeek
                ? {
                    ...hour,
                    open_time: formatTo24Hour(startTime),
                    close_time: formatTo24Hour(endTime),
                  }
                : hour
            );
            console.log('Updated Working Hours after calendar edit:', updatedHours);
            return updatedHours;
          });

          // Update barber object with new working hours
          barber.working_hours = barber.working_hours.map(wh =>
            wh.day_of_week === dayOfWeek
              ? {
                  ...wh,
                  start_time: formatTo24Hour(startTime),
                  end_time: formatTo24Hour(endTime)
                }
              : wh
          );
        } else {
          console.error('❌ No working hour found for day:', dayOfWeek);
          Alert.alert('Error', 'Failed to update working hours. Please try again.');
        }
      } catch (error) {
        console.error('❌ Failed to update working hours:', error?.response?.data || error);
        let errorMessage = 'Failed to update working hours.';
        if (error?.response?.data) {
          const data = error.response.data;
          errorMessage = Object.keys(data)
            .map(key => `${key}: ${Array.isArray(data[key]) ? data[key].join(', ') : data[key]}`)
            .join('\n');
        }
        Alert.alert('Error', errorMessage);
      } finally {
        setShowTimeModal(false);
      }
    };



    // Function to handle service deletion with confirmation
  const handleDeleteBarber = () => {
    console.log(`Deleting Service ${barberID}`);
    // Display the confirmation alert
    Alert.alert(
      'Confirm Deletion', // Alert title
      'Are you sure you want to delete this Service?', // Alert message
      [
        {
          text: 'Cancel',
          style: 'cancel',
          onPress: () => console.log('Deletion cancelled'),
        },
        {
          text: 'OK',
          onPress: async () => {
            try {
              // Call the delete API function
              await delete_barber(barberID);
              console.log(`✅ Deleted barber ${barberID}`);
              navigation.replace('Barber');
            } catch (error) {
              console.error(`❌ Failed to delete barber ${barberID}:`, error);
            }
          },
        },
      ],
      { cancelable: false } // Prevent dismissing the alert by tapping outside
    );
  };

    return (
      <SafeAreaView style={{flex: 1, backgroundColor: backgroundColor}}>
        <CustomHeader title={AppText.UPDATE_BARBER} leftIconType="back" />
        <ScrollView contentContainerStyle={{paddingBottom: hp(40)}}>
          <KeyboardAvoidingView behavior={Platform.OS ? 'height' : 'padding'} style={{flex: 1}}>
            <View style={{marginHorizontal: wp(5)}}>
              <View style={{alignSelf:"flex-end"}}>
                       <TouchableOpacity
            onPress={handleDeleteBarber}
            style={{
              backgroundColor: getDarK_mode_LightGrayBackground(),
              padding: wp(2.5),
              borderRadius: wp(5),
              marginRight: wp(3),
            }}>
            <Icon
              source={globalpath.delete_icon}
              size={wp(5)}
              tintColor={colors.red}
            />
          </TouchableOpacity>
              </View>
              <CustomTextInput
                label={AppText.BARBER_NAME_LABEL}
                placeholder={AppText.BARBER_PLACEHOLDER}
                value={formData.full_name}
                onChangeText={value => handleChange('full_name', value)}
              />

              <ResponsiveText size={4} color={getTextColor()}>
                {AppText.DESCRIPTION}
              </ResponsiveText>
              <View style={[styles.descriptionContainer, { borderColor: getDark_Theme(), backgroundColor: backgroundColor }]}>
                <TextInput
                  style={[
                    styles.descriptionInput,
                    { color: getTextColor() }
                  ]}
                  placeholder={AppText.DESCRIPTION}
                  placeholderTextColor={'grey'}
                  value={formData.description}
                  onChangeText={value => handleChange('description', value)}
                  multiline={true}
                />
              </View>

              <CustomTextInput
                label={AppText.EMAIL_LABEL}
                placeholder={AppText.ENTER_EMAIL}
                value={formData.email}
                onChangeText={value => handleChange('email', value)}
                    autoCapitalize="none"
              keyboardType="email-address"
              />

              <CustomTextInput
                label={AppText.PHONE_NUMBER}
                placeholder={AppText.ENTER_PHONE_NUMBER}
                value={formData.phone_number}
                onChangeText={value => handleChange('phone_number', value)}
              />
            </View>


            <View style={{marginHorizontal:wp(5)}}>
            <ResponsiveText size={4} color={getTextColor} margin={[hp(0), 0, hp(1), 0]} weight={'600'}>
              {AppText.SET_DATE_TIME}
            </ResponsiveText>


      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {weekDays.map((day, index) => (
          <View key={index} style={{ alignItems: 'center', marginHorizontal: wp(2) }}>
            <Add_Barber_Week_Card
              day={day.day}
              isSelected={day.isSelected}
              onPress={() => handleDayPress(index)}
            />
            {day.selectedTime ? (
              <ResponsiveText size={3.2} color={getTextColor()} margin={[hp(1),0,0,0]}>
                {day.selectedTime}
              </ResponsiveText>
            ) : null}
          </View>
        ))}
      </ScrollView>

      <Add_Barber_Time_Picker_Modal
        visible={showTimeModal}
        onClose={() => setShowTimeModal(false)}
        onSave={handleTimeSelect}
        initialStartTime={barber.working_hours[selectedDay]?.start_time ? new Date(`2000-01-01 ${barber.working_hours[selectedDay].start_time}`) : new Date()}
        initialEndTime={barber.working_hours[selectedDay]?.end_time ? new Date(`2000-01-01 ${barber.working_hours[selectedDay].end_time}`) : new Date()}
      />

    </View>





            <View style={styles.imageContainer}>
              <View style={styles.existingImageContainer}>
                <ResponsiveText size={4} color={getTextColor()} style={styles.imageLabel}>
                  {AppText.BARBER_IMAGE}
                </ResponsiveText>
                <Image
                  source={formData.profile_image ? { uri: formData.profile_image } : globalpath.logo}
                  style={styles.existingImage}
                  resizeMode="contain"
                />
              </View>

              <View style={styles.uploadContainer}>
                <ResponsiveText size={4} color={getTextColor()} style={styles.imageLabel} margin={[0,0,0,wp(4)]}>
                  {formData.profile_image ? AppText.UPLOAD_NEW_IMAGE : AppText.UPLOAD_BARBER_IMAGE}
                </ResponsiveText>
                <ImageUploadcard
                  onImageSelect={handleImageSelect}
                  initialImage={formData.profile_image}
                />
              </View>
            </View>

            <TouchableOpacity
              style={[styles.addButton]}
              onPress={handleSubmit}
              disabled={loading}
            >
              <ResponsiveText color={colors.white} size={4} weight="bold">
                {AppText.UPDATE_BARBER}
              </ResponsiveText>
            </TouchableOpacity>
          </KeyboardAvoidingView>
        </ScrollView>
        {loading ? <Loader /> : undefined}
      </SafeAreaView>
    );
  };

  export default BarberDetails;

  const styles = StyleSheet.create({
    input: {
      height: hp(15),
      borderWidth: 1,
      borderRadius: wp(2),
      paddingHorizontal: wp(4),
      fontSize: wp(3.8),
      marginVertical: hp(1),
      flex: 1,
    },
    addButton: {
      marginTop: hp(2),
      marginBottom: hp(4),
      padding: hp(2),
      borderRadius: wp(2),
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: colors.Light_theme_maincolour,
      marginHorizontal: wp(5),
    },
    imageContainer: {
      marginHorizontal: wp(5),
      marginTop: hp(2),
    },
    existingImageContainer: {
      marginBottom: hp(2),
    },
    uploadContainer: {
      marginBottom: hp(2),
      marginHorizontal: -wp(3.5),
    },
    imageLabel: {
      marginBottom: hp(1),
    },
    existingImage: {
      width: "100%",
      height: wp(40),
      borderRadius: wp(2),
      borderWidth: 1,
      borderColor: colors.grey,
      backgroundColor: colors.white,
    },
    descriptionContainer: {
      borderWidth: 1,
      borderRadius: wp(2),
      marginVertical: hp(1),
      minHeight: hp(15),
      maxHeight: hp(25),
    },
    descriptionInput: {
      padding: wp(4),
      fontSize: wp(3.8),
      textAlign: 'left',
      textAlignVertical: 'top',
      flex: 1,
    },
  });
