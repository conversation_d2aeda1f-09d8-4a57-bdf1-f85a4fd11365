import React from 'react';
import { View, TouchableOpacity, StyleSheet, ActivityIndicator, Image } from 'react-native';
import { colors } from '../../Custom/Colors';
import { hp, wp } from '../../Custom/Responsiveness';
import Icon from '../../Custom/Icon';
import ResponsiveText from '../../Custom/RnText';
import { globalpath } from '../../Custom/globalpath';
import { LinearGradient } from 'react-native-linear-gradient';
import useAppText from '../../Custom/AppText';
import { useNavigation } from '@react-navigation/native';

const BarberProfileHeader = ({
  fullname,
  email,
  onBackPress,
  isLoading = false,
  profile_image,
  isFromAdmin,
  barber

}) => {
      const AppText = useAppText();
      const navigation = useNavigation();
        const handleEditPress = () => {
    navigation.navigate('Barber_Details', { barber:barber });
  };

  return (
    <LinearGradient
      colors={[colors.Light_theme_maincolour, '#A17A3A']}
      style={styles.container}
    >
      {/* Back button and title */}
      <View style={styles.topRow}>
        <View style={{flexDirection:'row',alignItems:'center'}}>  
           <TouchableOpacity
          onPress={onBackPress}
          hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
          style={styles.backButton}
        >
          <Icon
            source={globalpath.goback}
           size={wp(5)}
            tintColor={colors.white}
          />
        </TouchableOpacity>
        <ResponsiveText size={4.8} color={colors.white} weight="500">
          {AppText.BARBER_PROFILE}
        </ResponsiveText>
        </View>
       {isFromAdmin && (
            <TouchableOpacity 
                onPress={handleEditPress}
                style={styles.editButtonContainer}
                hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}>
                <Icon 
                  source={globalpath.edit} 
                  style={styles.editIcon} 
                  tintColor={colors.white}
                />
              </TouchableOpacity>
          )}
      </View>

      {/* Profile content */}
      <View style={styles.profileContent}>
        {/* Profile image */}
        <View style={styles.profileImageContainer}>
          {profile_image ? (
            <Image
              source={{ uri: profile_image }}
              style={styles.profileImage}
              resizeMode="cover"
            />
          ) : (
            <Icon
              source={globalpath.profile_image}
              style={styles.profileImage}
              resizeMode="cover"
            />
          )}
        </View>

        {/* Profile info */}
        <View style={styles.profileInfo}>
          {isLoading ? (
            <ActivityIndicator color={colors.white} size="small" />
          ) : (
            <>
              <ResponsiveText size={5} weight="600" color={colors.white} textAlign="center">
                {fullname}
              </ResponsiveText>
              <ResponsiveText size={3.5} color={colors.white} textAlign="center" margin={[hp(0), 0, 0, 0]}>
                {email}
              </ResponsiveText>
              <View style={styles.ratingContainer}>
                <Icon
                  source={globalpath.stars}
                  size={15}
                  resizeMode="contain"
                  margin={[0, 0, wp(0.5), 0]}
                />
                <ResponsiveText size={3.5} weight="400" color={colors.white}>
                  Professional Barber
                </ResponsiveText>
              </View>
            </>
          )}
        </View>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingTop: hp(2),
    paddingBottom: hp(1),
    // borderBottomLeftRadius: wp(5),
    // borderBottomRightRadius: wp(5),
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    height: hp(25),

  },
  topRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: wp(5),
    marginBottom: hp(2),
    justifyContent: 'space-between',
  },
  backButton: {
    padding: wp(2),
    marginRight: wp(3),
  },
  backIcon: {
    height: hp(3),
    width: wp(3),
  },
  profileContent: {
    flexDirection: 'row',
    paddingHorizontal: wp(8),
    alignItems: 'center',
  },
  profileImageContainer: {
    width: wp(25),
    height: wp(25),
    borderRadius: wp(15),
    backgroundColor: colors.white,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: colors.white,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    // bottom: hp(2),

  },
  profileImage: {
    width: '100%',
    height: '100%',
  },
  profileInfo: {
    flex: 1,
    paddingLeft: wp(5),
    justifyContent: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: hp(0.5),
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingVertical: hp(0.5),
    paddingHorizontal: wp(3),
    borderRadius: wp(5),
    alignSelf: 'center',
  },
    editButtonContainer: {
    // marginTop: hp(6),
    backgroundColor: colors.Light_theme_maincolour,
    padding: wp(2),
    borderRadius: wp(9),
    borderWidth: 1,
    borderColor: colors.white,
    width: wp(8),
    height: wp(8),
    alignItems: 'center',
    justifyContent: 'center',
    // right: wp(5),
  },
});

export default BarberProfileHeader;
