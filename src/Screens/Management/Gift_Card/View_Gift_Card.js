import React from 'react';
import { StyleSheet, View, ScrollView, TouchableOpacity, Image, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import CustomHeader from '../../../Components/CustomHeader';
import useAppText from '../../../Custom/AppText';
import useTheme from '../../../Redux/useTheme';
import Icon from '../../../Custom/Icon';
import { colors } from '../../../Custom/Colors';
import ResponsiveText from '../../../Custom/RnText';
import { globalpath } from '../../../Custom/globalpath';
import { hp, wp } from '../../../Custom/Responsiveness';
import GiftCardDescription from '../../../Components/Gift_Cards/GiftCardDescription';
import { delete_giftcard_Form } from '../../../Services/API/Endpoints/Admin/Giftcard';

const View_Gift_Card = ({ route, navigation }) => {
  const { giftCard } = route.params;
  const { id } = giftCard;
  console.log("The gift card id is ", id)
  const AppText = useAppText();
  const { backgroundColor, getDark_Theme, getTextColor, getBellBackground } = useTheme();

  const handleEdit = () => {
    navigation.navigate('Edit_Gift_Card', { giftCard });
  };


      // Function to handle service deletion with confirmation
    const handleDeleteCard = () => {
      console.log(`Deleting Service ${id}`);
      // Display the confirmation alert
      Alert.alert(
        'Confirm Deletion', // Alert title
        'Are you sure you want to delete this Service?', // Alert message
        [
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => console.log('Deletion cancelled'),
          },
          {
            text: 'OK',
            onPress: async () => {
              try {
                // Call the delete API function
                await delete_giftcard_Form(id);
                console.log(`✅ Deleted gift card ${id}`);
                navigation.goBack();
              } catch (error) {
                console.error(`❌ Failed to delete gift card ${id}:`, error);
              }
            },
          },
        ],
        { cancelable: false } // Prevent dismissing the alert by tapping outside
      );
    };
  

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <CustomHeader title={AppText.GIFT_CARD_DETAILS} showBellIcon={false} leftIconType="back" />
      <View style={styles.imageContainer}>
        <Image
          source={giftCard.giftcard_image ? { uri: giftCard.giftcard_image } : globalpath.logo}
          style={styles.image}
          resizeMode="cover"
        />
      </View>
      <ScrollView style={styles.content}>
        <View style={styles.detailsContainer}>
          <View style={styles.titleRow}>
            <ResponsiveText
              color={getTextColor()}
              size={4}
              weight="600"
              flex={1}
            >
              {giftCard.product_name}
            </ResponsiveText>
                <TouchableOpacity 
              style={[styles.editButton, { backgroundColor: getBellBackground() , marginRight: wp(3) }]} 
              onPress={handleDeleteCard}
            >
              <Icon source={globalpath.delete_icon} size={wp(5)} tintColor={colors.red} />
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.editButton, { backgroundColor: getBellBackground() }]} 
              onPress={handleEdit}
            >
              <Icon source={globalpath.edit} size={wp(5)} tintColor={getTextColor()} />
            </TouchableOpacity>
          </View>

          <View style={styles.titleRow}>
            <ResponsiveText
              color={getTextColor()}
              size={4}
              // weight="bold"
              flex={1}
            >
              {giftCard.product_name}
            </ResponsiveText>

            <ResponsiveText
              color={colors.Light_theme_maincolour}
              size={4.5}
              weight="500"
            >
              ${giftCard.price}
            </ResponsiveText>
           
          </View>

          {/* <View style={styles.amountRow}>
            <ResponsiveText
              color={colors.Light_theme_maincolour}
              size={3.6}
              weight="bold"
            >
              ${giftCard.price}
            </ResponsiveText>
          </View> */}

          <View style={[styles.divider, { borderBottomColor: getDark_Theme() }]} />

          <GiftCardDescription description={giftCard.description} />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default View_Gift_Card;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  detailsContainer: {
    padding: wp(4),
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(2),
  },
  editButton: {
    padding: wp(2.5),
    borderRadius: wp(6)
  },
  amountRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(2),
  },
  divider: {
    borderBottomWidth: 1,
    marginBottom: hp(2),
  },
  imageContainer: {
    height: hp(40),
    backgroundColor: colors.lightGrey1,
    borderBottomRightRadius: wp(4),
    borderBottomLeftRadius: wp(4),
    marginBottom: hp(1),
    bottom: hp(1.5),
  },
  image: {
    width: '100%',
    height: '100%',
    borderBottomRightRadius: wp(4),
    borderBottomLeftRadius: wp(4),
  },
}); 